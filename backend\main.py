from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import uuid
import shutil
from pathlib import Path
from PIL import Image
import json
from datetime import datetime
from typing import Dict, List, Optional
import aiofiles

app = FastAPI(title="Video Editor API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Storage directories
STORAGE_DIR = Path("storage")
UPLOADS_DIR = STORAGE_DIR / "uploads"
PROXIES_DIR = STORAGE_DIR / "proxies"
THUMBNAILS_DIR = STORAGE_DIR / "thumbnails"

# Create directories
for dir_path in [UPLOADS_DIR, PROXIES_DIR, THUMBNAILS_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# Mount static files
app.mount("/storage", StaticFiles(directory="storage"), name="storage")

# In-memory storage for demo (replace with database in production)
media_storage: Dict[str, dict] = {}

class MediaFile:
    def __init__(self, id: str, name: str, original_path: str, proxy_path: str, 
                 thumbnail_path: str, duration: float, file_size: int):
        self.id = id
        self.name = name
        self.original_path = original_path
        self.proxy_path = proxy_path
        self.thumbnail_path = thumbnail_path
        self.duration = duration
        self.file_size = file_size
        self.created_at = datetime.now()

def get_video_duration(file_path: str) -> float:
    """Get video duration using ffprobe"""
    try:
        # 简化版本：使用文件大小估算时长（临时方案）
        # 实际项目中应该安装FFmpeg
        import os
        file_size = os.path.getsize(file_path)
        # 粗略估算：假设1MB约等于1秒（这只是临时方案）
        estimated_duration = max(10, file_size / (1024 * 1024))
        return min(estimated_duration, 300)  # 限制在5分钟内
    except Exception as e:
        print(f"Error getting duration: {e}")
        return 30.0  # 默认30秒

def create_video_proxy(input_path: str, output_path: str) -> bool:
    """Create a lower resolution proxy video"""
    try:
        # 临时方案：直接复制原文件作为代理
        # 实际项目中应该安装FFmpeg进行转码
        import shutil
        shutil.copy2(input_path, output_path)
        print(f"Proxy created (copy): {output_path}")
        return True
    except Exception as e:
        print(f"Error creating proxy: {e}")
        return False

def create_thumbnail(input_path: str, output_path: str, time_offset: float = 1.0) -> bool:
    """Create a thumbnail from video"""
    try:
        # 临时方案：创建一个简单的占位图片
        from PIL import Image, ImageDraw

        # 创建一个简单的缩略图
        img = Image.new('RGB', (320, 180), color='#333333')
        draw = ImageDraw.Draw(img)

        # 添加文本
        try:
            # 尝试使用默认字体
            draw.text((10, 80), "Video\nThumbnail", fill='white')
        except:
            # 如果字体加载失败，只画一个简单的矩形
            draw.rectangle([50, 50, 270, 130], outline='white', width=2)

        img.save(output_path, 'PNG')
        print(f"Thumbnail created: {output_path}")
        return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

@app.post("/api/upload")
async def upload_video(file: UploadFile = File(...)):
    """Upload and process video file"""
    if not file.content_type or not file.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Generate unique ID
    file_id = str(uuid.uuid4())
    
    # File paths
    original_filename = file.filename or f"video_{file_id}"
    file_extension = Path(original_filename).suffix
    
    original_path = UPLOADS_DIR / f"{file_id}{file_extension}"
    proxy_path = PROXIES_DIR / f"{file_id}_proxy.mp4"
    thumbnail_path = THUMBNAILS_DIR / f"{file_id}_thumb.png"
    
    try:
        # Save original file
        with open(original_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Get file size
        file_size = original_path.stat().st_size
        
        # Get video duration
        duration = get_video_duration(str(original_path))
        
        # Create proxy video
        proxy_success = create_video_proxy(str(original_path), str(proxy_path))
        if not proxy_success:
            raise HTTPException(status_code=500, detail="Failed to create video proxy")
        
        # Create thumbnail
        thumbnail_success = create_thumbnail(str(original_path), str(thumbnail_path))
        if not thumbnail_success:
            print("Warning: Failed to create thumbnail")
        
        # Store media info
        media_info = {
            "id": file_id,
            "name": original_filename,
            "duration": duration,
            "file_size": file_size,
            "proxy_url": f"/storage/proxies/{file_id}_proxy.mp4",
            "thumbnail_url": f"/storage/thumbnails/{file_id}_thumb.png" if thumbnail_success else None,
            "created_at": datetime.now().isoformat()
        }
        
        media_storage[file_id] = media_info
        
        return JSONResponse(content=media_info)
        
    except Exception as e:
        # Cleanup on error
        for path in [original_path, proxy_path, thumbnail_path]:
            if path.exists():
                path.unlink()
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.get("/api/media")
async def get_media_list():
    """Get list of all uploaded media"""
    return JSONResponse(content=list(media_storage.values()))

@app.get("/api/media/{media_id}")
async def get_media_info(media_id: str):
    """Get specific media information"""
    if media_id not in media_storage:
        raise HTTPException(status_code=404, detail="Media not found")
    
    return JSONResponse(content=media_storage[media_id])

@app.delete("/api/media/{media_id}")
async def delete_media(media_id: str):
    """Delete media file"""
    if media_id not in media_storage:
        raise HTTPException(status_code=404, detail="Media not found")
    
    # Remove files
    file_extension = Path(media_storage[media_id]["name"]).suffix
    original_path = UPLOADS_DIR / f"{media_id}{file_extension}"
    proxy_path = PROXIES_DIR / f"{media_id}_proxy.mp4"
    thumbnail_path = THUMBNAILS_DIR / f"{media_id}_thumb.png"
    
    for path in [original_path, proxy_path, thumbnail_path]:
        if path.exists():
            path.unlink()
    
    # Remove from storage
    del media_storage[media_id]
    
    return JSONResponse(content={"message": "Media deleted successfully"})

@app.get("/")
async def root():
    return {"message": "Video Editor API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
